# 🤖 WebSocket AI Backend

Backend completo en Node.js que integra WebSockets para comunicación en tiempo real, Speech-to-Text de Google Cloud, IA mediante Model Gateway API, y Text-to-Speech para generar respuestas de audio.

## ✨ Características

- **WebSockets en tiempo real** usando uWebSockets.js para máximo rendimiento
- **Speech-to-Text** con Google Cloud Speech API
- **Integración con IA** usando Model Gateway API (compatible con OpenAI)
- **Text-to-Speech** con Google Cloud Text-to-Speech API
- **Gestión de sesiones** persistentes
- **Soporte multipreset** para diferentes configuraciones de IA
- **Cliente frontend** de prueba incluido

## 🛠️ Tecnologías

- **Node.js** - Runtime de JavaScript
- **uWebSockets.js** - WebSockets de alto rendimiento
- **Google Cloud Speech** - Conversión de voz a texto
- **Google Cloud Text-to-Speech** - Conversión de texto a voz
- **Model Gateway API** - Integración con modelos de IA
- **Axios** - Cliente HTTP
- **UUID** - Generación de identificadores únicos

## 📋 Prerequisitos

- Node.js >= 16.0.0
- npm >= 8.0.0
- Proyecto de Google Cloud con APIs habilitadas:
  - Cloud Speech-to-Text API
  - Cloud Text-to-Speech API
- Credenciales de service account de Google Cloud
- Acceso al Model Gateway API con API key válida

## 🚀 Instalación

1. **Clonar el repositorio**
```bash
git clone https://github.com/tu-usuario/websocket-ai-backend.git
cd websocket-ai-backend
```

2. **Instalar dependencias**
```bash
npm install
```

3. **Configurar variables de entorno**
```bash
cp .env.example .env
# Editar .env con tus configuraciones
```

4. **Configurar Google Cloud**
   - Crear un proyecto en Google Cloud Console
   - Habilitar las APIs de Speech-to-Text y Text-to-Speech
   - Crear una service account y descargar las credenciales JSON
   - Configurar la ruta en `GOOGLE_APPLICATION_CREDENTIALS`

## ⚙️ Configuración

### Variables de entorno (.env)

```env
# Servidor
PORT=8080

# Model Gateway API
VITE_IA_API_URL=https://dev.dl2discovery.org/llm-api/v1/
VITE_IA_API_KEY=tu-api-key-aqui
VITE_IA_PRESETID_GENCHARBOT=mapp-gen-char-bot
VITE_IA_PRESETID_IA_VS_PLAYER=mapp-Claude_enygma_V2

# Google Cloud
GOOGLE_CLOUD_PROJECT_ID=tu-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account-key.json
```

### Configuración de Google Cloud Speech-to-Text

El sistema está configurado para:
- **Codificación**: WEBM_OPUS
- **Sample Rate**: 48000 Hz
- **Idiomas**: Español (principal) e Inglés (alternativo)
- **Características**: Puntuación automática, timestamps de palabras
- **Modelo**: latest_long (optimizado para audio largo)

### Configuración de Text-to-Speech

Por defecto usa:
- **Idioma**: es-ES (Español de España)
- **Voz**: Neural2-A (femenina)
- **Formato**: MP3

## 🏃‍♂️ Uso

### Iniciar el servidor

```bash
# Modo producción
npm start

# Modo desarrollo (con nodemon)
npm run dev
```

### Endpoints disponibles

- **WebSocket**: `ws://localhost:8080`
- **Health Check**: `http://localhost:8080/health`

## 📡 Protocolo WebSocket

### Mensajes del cliente al servidor

#### 1. Enviar chunk de audio
```json
{
  "type": "audio_chunk",
  "audioData": "base64-encoded-audio"
}
```

#### 2. Finalizar audio
```json
{
  "type": "audio_end"
}
```

#### 3. Mensaje de texto
```json
{
  "type": "text_message",
  "text": "Hola, ¿cómo estás?"
}
```

#### 4. Cambiar preset de IA
```json
{
  "type": "set_preset",
  "preset": "genCharBot"
}
```

#### 5. Reiniciar sesión
```json
{
  "type": "reset_session"
}
```

### Mensajes del servidor al cliente

#### 1. Conexión establecida
```json
{
  "type": "connection",
  "status": "connected",
  "sessionId": "uuid-sesion"
}
```

#### 2. Transcripción completada
```json
{
  "type": "transcription",
  "text": "Texto transcrito del audio"
}
```

#### 3. Respuesta de IA
```json
{
  "type": "ai_response",
  "text": "Respuesta del modelo de IA",
  "sessionId": "session-id-ia",
  "preset": "genCharBot"
}
```

#### 4. Audio de respuesta
```json
{
  "type": "audio_response",
  "audioData": "base64-encoded-mp3",
  "format": "mp3"
}
```

#### 5. Estado de procesamiento
```json
{
  "type": "processing",
  "stage": "speech_to_text|ai_processing|text_to_speech"
}
```

#### 6. Error
```json
{
  "type": "error",
  "error": "Descripción del error",
  "details": "Detalles adicionales",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🎯 Flujo de procesamiento

1. **Cliente conecta** → Se crea sesión única
2. **Audio recibido** → Conversión Speech-to-Text
3. **Texto procesado** → Enviado a Model Gateway API
4. **Respuesta IA** → Conversión Text-to-Speech
5. **Audio generado** → Enviado al cliente

## 🧪 Cliente de prueba

Incluye un cliente HTML/JavaScript completo con:
- Interfaz de usuario responsive
- Grabación de audio en tiempo real
- Chat en tiempo real con IA
- Reproducción automática de respuestas de audio
- Gestión de presets
- Indicadores de estado visual

Para usar el cliente:
1. Iniciar el servidor backend
2. Abrir `client.html` en un navegador moderno
3. Dar permisos de micrófono cuando se soliciten
4. Hacer clic en "Conectar"

## 📊 Model Gateway API Integration

### Presets soportados

- **genCharBot**: Bot de generación de personajes
- **iaVsPlayer**: IA para juegos contra jugador

### Características de la integración

- **Gestión automática de sesiones** con el Model Gateway
- **Soporte para parámetros personalizados** de modelos
- **Manejo de errores** robusto
- **Reset de sesiones** cuando se cambia de preset
- **Compatibilidad** con la API documentada

## 🔧 Desarrollo

### Estructura del proyecto

```
├── server.js          # Servidor principal
├── client.html        # Cliente de prueba
├── package.json       # Dependencias
├── .env.example       # Ejemplo de configuración
└── README.md          # Documentación
```

### Funciones principales

- `AIBackend`: Clase principal del servidor
- `speechToText()`: Procesamiento STT con Google Cloud
- `processWithIA()`: Integración con Model Gateway
- `processTextToSpeech()`: Generación TTS con Google Cloud
- `cleanupInactiveSessions()`: Limpieza automática de sesiones

## 🛡️ Seguridad

- **Autenticación** mediante API keys
- **Validación** de mensajes WebSocket
- **Timeouts** de sesiones inactivas (30 minutos)
- **Límites de tamaño** en mensajes WebSocket
- **Manejo seguro** de credenciales mediante variables de entorno

## ⚡ Optimización

- **uWebSockets.js** para máximo rendimiento
- **Compression** habilitada en WebSockets
- **Backpressure handling** para evitar sobrecarga
- **Cleanup automático** de sesiones inactivas
- **Buffer reutilizable** para audio chunks

## 🐛 Solución de problemas

### Error: "STT processing failed"
- Verificar credenciales de Google Cloud
- Asegurar que el audio esté en formato correcto
- Verificar permisos de la service account

### Error: "IA processing failed"
- Verificar API key del Model Gateway
- Comprobar que el preset existe
- Revisar conectividad con el servidor

### Error: "TTS processing failed"
- Verificar configuración de Text-to-Speech
- Comprobar límites de cuota en Google Cloud
- Verificar que el texto no esté vacío

### WebSocket se desconecta frecuentemente
- Revisar configuración de red/firewall
- Verificar timeouts del servidor
- Comprobar estabilidad de conexión

## 📈 Monitoreo

El servidor incluye logs detallados para:
- Conexiones WebSocket
- Procesamiento de audio
- Llamadas a APIs externas
- Errores y excepciones
- Gestión de sesiones

## 🤝 Contribuir

1. Fork el proyecto
2. Crear rama para nueva funcionalidad (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📝 License

MIT License - ver archivo [LICENSE](LICENSE) para detalles.

## 📞 Soporte

Para soporte técnico:
- Crear issue en GitHub
- Revisar logs del servidor para debugging
- Verificar configuración de variables de entorno

---

**¡Listo para crear experiencias conversacionales increíbles! 🚀**